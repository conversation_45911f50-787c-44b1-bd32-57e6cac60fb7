Using pre-set license
Built from '6000.1/staging' branch; Version is '6000.1.10f1 (3c681a6c22ff) revision 3958810'; Using compiler version '194234433'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32676 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1
Date: 2025-08-05T20:40:50Z

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.1.10f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
C:/Projects/Unity/ProjectCourt
-logFile
Logs/AssetImportWorker0.log
-srvPort
65357
-job-worker-count
15
-background-job-worker-count
8
-gc-helper-count
1
-name
AssetImport
Successfully changed project path to: C:/Projects/Unity/ProjectCourt
C:/Projects/Unity/ProjectCourt
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [21500]  Target information:

Player connection [21500]  * "[IP] ************* [Port] 0 [Flags] 2 [Guid] 1788409716 [EditorId] 1788409716 [Version] 1048832 [Id] WindowsEditor(7,Bastian) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [21500] Host joined multi-casting on [***********:54997]...
Player connection [21500] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 15
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 2.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.1.10f1 (3c681a6c22ff)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path C:/Projects/Unity/ProjectCourt/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:         Direct3D 12 [level 12.1]
    Renderer:        AMD Radeon RX 6800 XT (ID=0x73bf)
    Vendor:          ATI
    VRAM:            16338 MB
    App VRAM Budget: 15570 MB
    Driver:          32.0.21025.1024
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56584
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.1.10f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.003005 seconds.
- Loaded All Assemblies, in  0.406 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 239 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.629 seconds
Domain Reload Profiling: 1033ms
	BeginReloadAssembly (146ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (40ms)
	RebuildNativeTypeToScriptingClass (11ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (158ms)
		LoadAssemblies (144ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (154ms)
			TypeCache.Refresh (153ms)
				TypeCache.ScanAssembly (141ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (630ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (584ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (374ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (102ms)
			ProcessInitializeOnLoadMethodAttributes (51ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.709 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 1.17 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Package Manager log level set to [2]
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.677 seconds
Domain Reload Profiling: 1381ms
	BeginReloadAssembly (148ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (5ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (29ms)
	RebuildCommonClasses (30ms)
	RebuildNativeTypeToScriptingClass (9ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (483ms)
		LoadAssemblies (312ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (191ms)
				TypeCache.ScanAssembly (171ms)
			BuildScriptInfoCaches (49ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (677ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (505ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (18ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (109ms)
			ProcessInitializeOnLoadAttributes (325ms)
			ProcessInitializeOnLoadMethodAttributes (45ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (5ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.01 seconds
Refreshing native plugins compatible for Editor in 1.15 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 260 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7866 unused Assets / (4.4 MB). Loaded Objects now: 8638.
Memory consumption went from 188.0 MB to 183.6 MB.
Total: 8.294200 ms (FindLiveObjects: 0.756500 ms CreateObjectMapping: 0.468700 ms MarkObjects: 4.729800 ms  DeleteObjects: 2.336800 ms)

========================================================================
Received Import Request.
  Time since last request: 46685.112193 seconds.
  path: Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate
  artifactKey: Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/ExampleContent/Scenes/EmptyScene.scenetemplate using Guid(8cb4b5eb3e9b0aa47bc6aec33d491f1d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: '9ef8239669e1359d2ebee67f63299966') in 0.7978696 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7480

========================================================================
Received Import Request.
  Time since last request: 2.035722 seconds.
  path: Assets/HorrorEngine/Localization/HELocalizationSettings.asset
  artifactKey: Guid(bad8e076199628443b9beeecefe6745e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Localization/HELocalizationSettings.asset using Guid(bad8e076199628443b9beeecefe6745e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '32558c95c2a0fee8719a6f5c01243eac') in 0.0063885 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Import Request.
  Time since last request: 0.304862 seconds.
  path: Assets/HorrorEngine/Prefabs/BlackBox.prefab
  artifactKey: Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/BlackBox.prefab using Guid(********************************) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '74e9171c6dd2f68bbdd9b5968c2ccdfb') in 0.4598031 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 35

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/HorrorEngine/Prefabs/PlayerSpawnPoint.prefab
  artifactKey: Guid(455a0d83e7921ab4e9335a2fe24b143f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PlayerSpawnPoint.prefab using Guid(455a0d83e7921ab4e9335a2fe24b143f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bbd66971271ae8ccb048e07082db34f4') in 0.0018035 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000040 seconds.
  path: Assets/HorrorEngine/Prefabs/InputManager.prefab
  artifactKey: Guid(74a3a146becc1104b9aaaec2e69f8da5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/InputManager.prefab using Guid(74a3a146becc1104b9aaaec2e69f8da5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '817b14785e11e8aaa59f339a0aeb85f9') in 0.0047635 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 31

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/HorrorEngine/Prefabs/ExamineItemRenderer.prefab
  artifactKey: Guid(8ef5d8f6877c90d47bba1b9ac32ee283) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ExamineItemRenderer.prefab using Guid(8ef5d8f6877c90d47bba1b9ac32ee283) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f94362e7ac37f0927d10ccc0edc60dfd') in 0.0026294 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/HorrorEngine/Prefabs/EventSystem.prefab
  artifactKey: Guid(a413bf7cd237ddb488e196d0ca1a1dac) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/EventSystem.prefab using Guid(a413bf7cd237ddb488e196d0ca1a1dac) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '51f9d2311517b556dfc28024a71a77bf') in 0.0024596 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 17

========================================================================
Received Import Request.
  Time since last request: 0.000024 seconds.
  path: Assets/HorrorEngine/Prefabs/InvisibleCollision.prefab
  artifactKey: Guid(e76dacc844c1bff45883eedf3fb426bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/InvisibleCollision.prefab using Guid(e76dacc844c1bff45883eedf3fb426bb) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'aef56bb616864c808977b9c13fbde8ad') in 0.1327288 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 11

========================================================================
Received Import Request.
  Time since last request: 0.000035 seconds.
  path: Assets/HorrorEngine/Prefabs/CutsceneSignalReceiver.prefab
  artifactKey: Guid(dc2ab0df1c9438f46b47e9bede4e7e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/CutsceneSignalReceiver.prefab using Guid(dc2ab0df1c9438f46b47e9bede4e7e4a) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '731c605ed92f3f7e0dc32aec9e54534a') in 0.0053135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 12

========================================================================
Received Import Request.
  Time since last request: 0.000037 seconds.
  path: Assets/HorrorEngine/Prefabs/PushableWithWeight.prefab
  artifactKey: Guid(2e78891c30d79a94482d5746d70d5885) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PushableWithWeight.prefab using Guid(2e78891c30d79a94482d5746d70d5885) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1f51f14043163aef5c52e3e8f65272be') in 0.0085381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 32

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/HorrorEngine/Prefabs/DoorTransitionController.prefab
  artifactKey: Guid(2adb7a86e6ac93242b1a803b0a532c45) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/DoorTransitionController.prefab using Guid(2adb7a86e6ac93242b1a803b0a532c45) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ab67cb2043c9364835f76f907fd54181') in 0.002311 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000028 seconds.
  path: Assets/HorrorEngine/Prefabs/ClimbableVault.prefab
  artifactKey: Guid(0051d142ba601e04f9f013fdccec6564) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ClimbableVault.prefab using Guid(0051d142ba601e04f9f013fdccec6564) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7bda701e1542f1a4e0399f2d9ec8c20d') in 0.0051838 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 30

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/HorrorEngine/Prefabs/Choice.prefab
  artifactKey: Guid(b63ad56d59a69204691969ca28e8b98e) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/Choice.prefab using Guid(b63ad56d59a69204691969ca28e8b98e) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cf80b69c49bffc97d28905c826a5acd5') in 0.0031498 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 13

========================================================================
Received Import Request.
  Time since last request: 0.000032 seconds.
  path: Assets/HorrorEngine/Prefabs/Door.prefab
  artifactKey: Guid(3d1853d82822fe440aaecd158d2e9b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/Door.prefab using Guid(3d1853d82822fe440aaecd158d2e9b1c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fe28982062529f1de2548480a9d45182') in 0.0038334 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000036 seconds.
  path: Assets/HorrorEngine/Prefabs/PlayerInput.prefab
  artifactKey: Guid(240938ef319e75a4188f94c5cd104141) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PlayerInput.prefab using Guid(240938ef319e75a4188f94c5cd104141) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '59a9f813611d950ed9f50944e1dbda9a') in 0.0027423 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/HorrorEngine/Prefabs/ClimbableLadder.prefab
  artifactKey: Guid(701bb4602947ae54cbab14d4433fad3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ClimbableLadder.prefab using Guid(701bb4602947ae54cbab14d4433fad3c) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '45b9413f9d74adce6fd66b9b5e24380a') in 0.0351621 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 71

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/HorrorEngine/Prefabs/SceneTransitionController.prefab
  artifactKey: Guid(67927f3b37525b545a0455b93da23fcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/SceneTransitionController.prefab using Guid(67927f3b37525b545a0455b93da23fcc) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7c4466003080bfc9ec1c88bede50232c') in 0.0021868 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5

========================================================================
Received Import Request.
  Time since last request: 0.000029 seconds.
  path: Assets/HorrorEngine/Prefabs/ItemExamination.prefab
  artifactKey: Guid(4d827492396d6fb4f9cf523a4bb98263) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ItemExamination.prefab using Guid(4d827492396d6fb4f9cf523a4bb98263) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '05a8419e060094ce24f53bc094c245e9') in 0.0020326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 6

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/HorrorEngine/Prefabs/HECore.prefab
  artifactKey: Guid(193bf82098c71a341a2c6f67ffa422b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/HECore.prefab using Guid(193bf82098c71a341a2c6f67ffa422b5) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter)Refreshing native plugins compatible for Editor in 1.53 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
 -> (artifact id: 'f7777b9ff1812e940f588d358155c36d') in 0.4168427 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 5965

========================================================================
Received Import Request.
  Time since last request: 0.000043 seconds.
  path: Assets/HorrorEngine/Prefabs/LocalContainer.prefab
  artifactKey: Guid(69072a8cd8bc11148a5ffa8588ac6406) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/LocalContainer.prefab using Guid(69072a8cd8bc11148a5ffa8588ac6406) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '692c58486c36e34b73fb3ee3a10f8ebc') in 0.0045222 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000041 seconds.
  path: Assets/HorrorEngine/Prefabs/CrossSceneDoor.prefab
  artifactKey: Guid(4cd439577a6198c42aaf77f758b17070) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/CrossSceneDoor.prefab using Guid(4cd439577a6198c42aaf77f758b17070) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '07148e7ea65ddf2973c4634cdf3389b2') in 0.003996 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000025 seconds.
  path: Assets/HorrorEngine/Prefabs/CloseupImage.prefab
  artifactKey: Guid(dede24e04b7814646aff051145f80e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/CloseupImage.prefab using Guid(dede24e04b7814646aff051145f80e70) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '60b1e649a1ef014d8ab14bd8ccfe2c28') in 0.0030975 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/HorrorEngine/Prefabs/PickupDocument.prefab
  artifactKey: Guid(b015e31022d5dad4fb3f485ff3e51321) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PickupDocument.prefab using Guid(b015e31022d5dad4fb3f485ff3e51321) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'b5d90c89c1aed0aa79efb21c9b43edab') in 0.0037381 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Import Request.
  Time since last request: 0.000034 seconds.
  path: Assets/HorrorEngine/Prefabs/PickupItem.prefab
  artifactKey: Guid(5c73ef28b5f95b14b92bec958713307d) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PickupItem.prefab using Guid(5c73ef28b5f95b14b92bec958713307d) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '147d034bf5d7b81c1db0e59db5f1a7af') in 0.0042899 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000038 seconds.
  path: Assets/HorrorEngine/Prefabs/PlayerTrigger.prefab
  artifactKey: Guid(bd0ff52a41201aa4abe0ef7c9e553905) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/PlayerTrigger.prefab using Guid(bd0ff52a41201aa4abe0ef7c9e553905) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '78048cd5c87368c96d0dbcbb8abaef74') in 0.2039494 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000030 seconds.
  path: Assets/HorrorEngine/Prefabs/ClimbableObstacle.prefab
  artifactKey: Guid(2c1e0ca37d2efab4ca937a11e76f1b6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ClimbableObstacle.prefab using Guid(2c1e0ca37d2efab4ca937a11e76f1b6f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '3d51a8eb314f11ff115d92d869802058') in 0.003058 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 18

========================================================================
Received Import Request.
  Time since last request: 0.000031 seconds.
  path: Assets/HorrorEngine/Prefabs/FixedCamera.prefab
  artifactKey: Guid(57be7cbfb83c77b4196f49c1698080c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/FixedCamera.prefab using Guid(57be7cbfb83c77b4196f49c1698080c1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1d93c6da3521f275343f1ccc7726b160') in 0.0020824 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 14

========================================================================
Received Import Request.
  Time since last request: 0.000023 seconds.
  path: Assets/HorrorEngine/Prefabs/ControlSchemeDetector.prefab
  artifactKey: Guid(97854e73c2562fa4dbedbed4a8e20fff) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/ControlSchemeDetector.prefab using Guid(97854e73c2562fa4dbedbed4a8e20fff) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'cb344f94d6a184a25687b14adfe243c4') in 0.0022531 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 7

========================================================================
Received Import Request.
  Time since last request: 0.000026 seconds.
  path: Assets/HorrorEngine/Prefabs/Interaction.prefab
  artifactKey: Guid(c10c3cc2aac81144a8b7872c3df610a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/Interaction.prefab using Guid(c10c3cc2aac81144a8b7872c3df610a0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '232066323ca71a7925b76ca2240daee8') in 0.0023168 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 8

========================================================================
Received Import Request.
  Time since last request: 0.000027 seconds.
  path: Assets/HorrorEngine/Prefabs/FixedCameraWithShape.prefab
  artifactKey: Guid(76f50431766ac444495e1ed33da68aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/HorrorEngine/Prefabs/FixedCameraWithShape.prefab using Guid(76f50431766ac444495e1ed33da68aa2) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '043e245b28bf447555638b3faf58d87e') in 0.0167149 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 19

========================================================================
Received Prepare
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
Begin MonoManager ReloadAssembly
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in  0.978 seconds
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
'Packages/com.unity.cinemachine/Editor/Obsolete/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
'Packages/com.unity.cinemachine/Editor/Editors/CinemachineVirtualCameraBaseEditor.cs' contains partial class of Unity.Object, exactly 1 of the partial classes needs to be in a file with the same filename. This is to know the one that can be referenced from ie. GameObjects
Refreshing native plugins compatible for Editor in 0.97 ms, found 3 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Assembly reference Packages/com.unity.cinemachine/Editor/Samples/ExposeHDRPInternals/HDRP-Editor-ref.asmref has no target assembly definition
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
[Package Manager] Failed to launch server process. Reason: Unity was launched with the -noUpm command-line argument
[Package Manager] Unable to send message (not connected to server process).
[Package Manager] Cannot connect to Unity Package Manager local server
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.715 seconds
Domain Reload Profiling: 1693ms
	BeginReloadAssembly (316ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (26ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (115ms)
	RebuildCommonClasses (55ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (555ms)
		LoadAssemblies (462ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (205ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (5ms)
			BuildScriptInfoCaches (170ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (715ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (543ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (19ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (131ms)
			ProcessInitializeOnLoadAttributes (327ms)
			ProcessInitializeOnLoadMethodAttributes (56ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (15ms)
Refreshing native plugins compatible for Editor in 1.07 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 22 Unused Serialized files (Serialized files now loaded: 0)
Unloading 7875 unused Assets / (4.6 MB). Loaded Objects now: 8694.
Memory consumption went from 160.2 MB to 155.5 MB.
Total: 8.189200 ms (FindLiveObjects: 0.782400 ms CreateObjectMapping: 0.575500 ms MarkObjects: 4.442900 ms  DeleteObjects: 2.388000 ms)

Prepare: number of updated asset objects reloaded= 0
Editor requested this worker to shutdown with reason: Scaling down because of idle timeout
AssetImportWorker is now disconnected from the server
Process exiting
Exiting without the bug reporter. Application will terminate with return code 0